-- 会务表
DROP TABLE IF EXISTS CONF_BASIC;
CREATE TABLE CONF_BASIC(
                           ID BIGINT NOT NULL,
                           CODE VARCHAR(255),
                           NAME VARCHAR(255),
                           START_TIME DATETIME,
                           END_TIME DATETIME,
                           SIGNUP_DEADLINE DATETIME,
                           PRESET_JSON VARCHAR(4000),
                           BANNER_URL VARCHAR(255),
                           BANNER_COLOR_CLASS VARCHAR(255),
                           BANNER_TITLE VARCHAR(255),
                           BANNER_TITLE_COLOR_CLASS VARCHAR(255),
                           BANNER_SUBTITLE VARCHAR(255),
                           BANNER_SUBTITLE_COLOR_CLASS VARCHAR(255),
                           BACKGROUND_URL VARCHAR(255),
                           BACKGROUND_COLOR_CLASS VARCHAR(255),
                           USE_BOARD VARCHAR(2),
                           STATUS VARCHAR(2),
                           CREATE_BY VARCHAR(36),
                           CREATE_TIME DATETIME,
                           UPDATE_BY VARCHAR(36),
                           UPDATE_TIME DATETIME,
                           DEL_FLAG CHAR(1) NOT NULL DEFAULT  0,
                           PRIMARY KEY (ID)
);

COMMENT ON TABLE CONF_BASIC IS '会务主表';
COMMENT ON COLUMN CONF_BASIC.ID IS '主键';
COMMENT ON COLUMN CONF_BASIC.CODE IS '会务代码;用于签到、报名地址';
COMMENT ON COLUMN CONF_BASIC.NAME IS '会务名称';
COMMENT ON COLUMN CONF_BASIC.START_TIME IS '开始时间';
COMMENT ON COLUMN CONF_BASIC.END_TIME IS '结束时间';
COMMENT ON COLUMN CONF_BASIC.SIGNUP_DEADLINE IS '报名截止时间';
COMMENT ON COLUMN CONF_BASIC.PRESET_JSON IS '首页模块JSON';
COMMENT ON COLUMN CONF_BASIC.BANNER_URL IS 'banner图片路径';
COMMENT ON COLUMN CONF_BASIC.BANNER_COLOR_CLASS IS 'banner颜色';
COMMENT ON COLUMN CONF_BASIC.BANNER_TITLE IS 'banner标题';
COMMENT ON COLUMN CONF_BASIC.BANNER_TITLE_COLOR_CLASS IS 'banner标题颜色';
COMMENT ON COLUMN CONF_BASIC.BANNER_SUBTITLE IS 'banner子标题';
COMMENT ON COLUMN CONF_BASIC.BANNER_SUBTITLE_COLOR_CLASS IS 'banner子标题颜色';
COMMENT ON COLUMN CONF_BASIC.BACKGROUND_URL IS '底图图片路径';
COMMENT ON COLUMN CONF_BASIC.BACKGROUND_COLOR_CLASS IS '底图颜色';
COMMENT ON COLUMN CONF_BASIC.USE_BOARD IS '是否使用留言板';
COMMENT ON COLUMN CONF_BASIC.STATUS IS '会务状态;0 草稿 1 发布 2 结束';
COMMENT ON COLUMN CONF_BASIC.CREATE_BY IS '创建人';
COMMENT ON COLUMN CONF_BASIC.CREATE_TIME IS '创建时间';
COMMENT ON COLUMN CONF_BASIC.UPDATE_BY IS '更新人';
COMMENT ON COLUMN CONF_BASIC.UPDATE_TIME IS '更新时间';
COMMENT ON COLUMN CONF_BASIC.DEL_FLAG IS '删除标记;0 不删除 1 删除';

-- 会务模块
DROP TABLE IF EXISTS CONF_MODULE;
CREATE TABLE CONF_MODULE(
                            ID BIGINT NOT NULL,
                            MODULE_TYPE VARCHAR(255),
                            WIDTH VARCHAR(255),
                            HEIGHT VARCHAR(255),
                            SORT INT,
                            PATH VARCHAR(255),
                            ICON_CLASS VARCHAR(255),
                            TITLE VARCHAR(255),
                            SUBTITLE VARCHAR(255),
                            TEXT_COLOR_CLASS VARCHAR(255),
                            BG_COLOR_CLASS VARCHAR(255),
                            BG_IMG_URL VARCHAR(255),
                            CREATE_BY VARCHAR(36),
                            CREATE_TIME DATETIME,
                            UPDATE_BY VARCHAR(36),
                            UPDATE_TIME DATETIME,
                            DEL_FLAG VARCHAR(1) NOT NULL DEFAULT  0,
                            PRIMARY KEY (ID)
);

COMMENT ON TABLE CONF_MODULE IS '会务ui模块';
COMMENT ON COLUMN CONF_MODULE.ID IS '主键';
COMMENT ON COLUMN CONF_MODULE.MODULE_TYPE IS '组件类型';
COMMENT ON COLUMN CONF_MODULE.WIDTH IS '宽度;half|wide';
COMMENT ON COLUMN CONF_MODULE.HEIGHT IS '高度;1x|2x';
COMMENT ON COLUMN CONF_MODULE.SORT IS '排序';
COMMENT ON COLUMN CONF_MODULE.PATH IS '移动端路由';
COMMENT ON COLUMN CONF_MODULE.ICON_CLASS IS '图标样式';
COMMENT ON COLUMN CONF_MODULE.TITLE IS '标题';
COMMENT ON COLUMN CONF_MODULE.SUBTITLE IS '子标题';
COMMENT ON COLUMN CONF_MODULE.TEXT_COLOR_CLASS IS '文字颜色样式';
COMMENT ON COLUMN CONF_MODULE.BG_COLOR_CLASS IS '颜色样式';
COMMENT ON COLUMN CONF_MODULE.BG_IMG_URL IS '背景图路径';
COMMENT ON COLUMN CONF_MODULE.CREATE_BY IS '创建人';
COMMENT ON COLUMN CONF_MODULE.CREATE_TIME IS '创建时间';
COMMENT ON COLUMN CONF_MODULE.UPDATE_BY IS '更新人';
COMMENT ON COLUMN CONF_MODULE.UPDATE_TIME IS '更新时间';
COMMENT ON COLUMN CONF_MODULE.DEL_FLAG IS '删除标记;0 不删除 1 删除';

-- 会务模块预设
DROP TABLE IF EXISTS CONF_MODULE_PRESET;
CREATE TABLE CONF_MODULE_PRESET(
                                   ID BIGINT NOT NULL,
                                   PRESET_NAME VARCHAR(255),
                                   PRESET_JSON VARCHAR(4000),
                                   BANNER_URL VARCHAR(255),
                                   BANNER_COLOR_CLASS VARCHAR(255),
                                   BANNER_TITLE VARCHAR(255),
                                   BANNER_TITLE_COLOR_CLASS VARCHAR(255),
                                   BANNER_SUBTITLE VARCHAR(255),
                                   BANNER_SUBTITLE_COLOR_CLASS VARCHAR(255),
                                   BACKGROUND_URL VARCHAR(255),
                                   BACKGROUND_COLOR_CLASS VARCHAR(255),
                                   CREATE_BY VARCHAR(36),
                                   CREATE_TIME DATETIME,
                                   UPDATE_BY VARCHAR(36),
                                   UPDATE_TIME DATETIME,
                                   DEL_FLAG CHAR(1) NOT NULL DEFAULT  0,
                                   PRIMARY KEY (ID)
);

COMMENT ON TABLE CONF_MODULE_PRESET IS '会务首页设计预设';
COMMENT ON COLUMN CONF_MODULE_PRESET.ID IS '主键';
COMMENT ON COLUMN CONF_MODULE_PRESET.PRESET_NAME IS '预设名称';
COMMENT ON COLUMN CONF_MODULE_PRESET.PRESET_JSON IS '预设首页模块json';
COMMENT ON COLUMN CONF_MODULE_PRESET.BANNER_URL IS 'banner图片路径';
COMMENT ON COLUMN CONF_MODULE_PRESET.BANNER_COLOR_CLASS IS 'banner颜色';
COMMENT ON COLUMN CONF_MODULE_PRESET.BANNER_TITLE IS 'banner标题';
COMMENT ON COLUMN CONF_MODULE_PRESET.BANNER_TITLE_COLOR_CLASS IS 'banner标题颜色';
COMMENT ON COLUMN CONF_MODULE_PRESET.BANNER_SUBTITLE IS 'banner子标题';
COMMENT ON COLUMN CONF_MODULE_PRESET.BANNER_SUBTITLE_COLOR_CLASS IS 'banner子标题颜色';
COMMENT ON COLUMN CONF_MODULE_PRESET.BACKGROUND_URL IS '底图图片路径';
COMMENT ON COLUMN CONF_MODULE_PRESET.BACKGROUND_COLOR_CLASS IS '底图颜色';
COMMENT ON COLUMN CONF_MODULE_PRESET.CREATE_BY IS '创建人';
COMMENT ON COLUMN CONF_MODULE_PRESET.CREATE_TIME IS '创建时间';
COMMENT ON COLUMN CONF_MODULE_PRESET.UPDATE_BY IS '更新人';
COMMENT ON COLUMN CONF_MODULE_PRESET.UPDATE_TIME IS '更新时间';
COMMENT ON COLUMN CONF_MODULE_PRESET.DEL_FLAG IS '删除标记;0 不删除 1 删除';

-- 会务地址表
DROP TABLE IF EXISTS CONF_ADDR;
CREATE TABLE CONF_ADDR(
                          ID BIGINT NOT NULL,
                          CONF_ID BIGINT,
                          DESC_INFO VARCHAR(255),
                          CONF_ADDR VARCHAR(255),
                          LOC_ID VARCHAR(255),
                          LOC_NAME VARCHAR(255),
                          LOC VARCHAR(255),
                          LOC_FULL_ADDR VARCHAR(255),
                          CREATE_BY VARCHAR(36),
                          CREATE_TIME DATETIME,
                          UPDATE_BY VARCHAR(36),
                          UPDATE_TIME DATETIME,
                          DEL_FLAG CHAR(1) NOT NULL DEFAULT  0,
                          PRIMARY KEY (ID)
);

COMMENT ON TABLE CONF_ADDR IS '会务地址表';
COMMENT ON COLUMN CONF_ADDR.ID IS '主键';
COMMENT ON COLUMN CONF_ADDR.CONF_ID IS '会务id';
COMMENT ON COLUMN CONF_ADDR.DESC_INFO IS '描述信息';
COMMENT ON COLUMN CONF_ADDR.CONF_ADDR IS '会务地址';
COMMENT ON COLUMN CONF_ADDR.LOC_ID IS '定位id';
COMMENT ON COLUMN CONF_ADDR.LOC_NAME IS '定位名称';
COMMENT ON COLUMN CONF_ADDR.LOC IS '定位经纬度';
COMMENT ON COLUMN CONF_ADDR.LOC_FULL_ADDR IS '定位地址信息';
COMMENT ON COLUMN CONF_ADDR.CREATE_BY IS '创建人';
COMMENT ON COLUMN CONF_ADDR.CREATE_TIME IS '创建时间';
COMMENT ON COLUMN CONF_ADDR.UPDATE_BY IS '更新人';
COMMENT ON COLUMN CONF_ADDR.UPDATE_TIME IS '更新时间';
COMMENT ON COLUMN CONF_ADDR.DEL_FLAG IS '删除标记;0 不删除 1 删除';

-- 会务组
DROP TABLE IF EXISTS CONF_STAFF_GROUP;
CREATE TABLE CONF_STAFF_GROUP(
                                 ID BIGINT NOT NULL,
                                 CONF_ID BIGINT,
                                 GROUP_NAME VARCHAR(255),
                                 STAFF_JSON VARCHAR(1000),
                                 SORT INT,
                                 CREATE_BY VARCHAR(36),
                                 CREATE_TIME DATETIME,
                                 UPDATE_BY VARCHAR(36),
                                 UPDATE_TIME DATETIME,
                                 DEL_FLAG CHAR(1) NOT NULL DEFAULT  0,
                                 PRIMARY KEY (ID)
);

COMMENT ON TABLE CONF_STAFF_GROUP IS '会务组';
COMMENT ON COLUMN CONF_STAFF_GROUP.ID IS '主键';
COMMENT ON COLUMN CONF_STAFF_GROUP.CONF_ID IS '会务id';
COMMENT ON COLUMN CONF_STAFF_GROUP.GROUP_NAME IS '组名称';
COMMENT ON COLUMN CONF_STAFF_GROUP.STAFF_JSON IS '组人员信息';
COMMENT ON COLUMN CONF_STAFF_GROUP.SORT IS '排序';
COMMENT ON COLUMN CONF_STAFF_GROUP.CREATE_BY IS '创建人';
COMMENT ON COLUMN CONF_STAFF_GROUP.CREATE_TIME IS '创建时间';
COMMENT ON COLUMN CONF_STAFF_GROUP.UPDATE_BY IS '更新人';
COMMENT ON COLUMN CONF_STAFF_GROUP.UPDATE_TIME IS '更新时间';
COMMENT ON COLUMN CONF_STAFF_GROUP.DEL_FLAG IS '删除标记;0 不删除 1 删除';

-- 会务图文目录表
DROP TABLE IF EXISTS CONF_GALLERY;
CREATE TABLE CONF_GALLERY(
                             ID BIGINT NOT NULL,
                             CONF_ID BIGINT,
                             GALLERY_NAME VARCHAR(255),
                             GALLERY_COVER VARCHAR(255),
                             GALLERY_DATE VARCHAR(255),
                             GALLERY_LOCATION VARCHAR(255),
                             SORT INT,
                             CREATE_BY VARCHAR(36),
                             CREATE_TIME DATETIME,
                             UPDATE_BY VARCHAR(36),
                             UPDATE_TIME DATETIME,
                             DEL_FLAG VARCHAR(1) NOT NULL DEFAULT  0,
                             PRIMARY KEY (ID)
);

COMMENT ON TABLE CONF_GALLERY IS '会务图文目录表';
COMMENT ON COLUMN CONF_GALLERY.ID IS '主键';
COMMENT ON COLUMN CONF_GALLERY.CONF_ID IS '会务ID';
COMMENT ON COLUMN CONF_GALLERY.GALLERY_NAME IS '目录名称';
COMMENT ON COLUMN CONF_GALLERY.GALLERY_COVER IS '封面';
COMMENT ON COLUMN CONF_GALLERY.GALLERY_DATE IS '日期';
COMMENT ON COLUMN CONF_GALLERY.GALLERY_LOCATION IS '地点';
COMMENT ON COLUMN CONF_GALLERY.SORT IS '排序';
COMMENT ON COLUMN CONF_GALLERY.CREATE_BY IS '创建人';
COMMENT ON COLUMN CONF_GALLERY.CREATE_TIME IS '创建时间';
COMMENT ON COLUMN CONF_GALLERY.UPDATE_BY IS '更新人';
COMMENT ON COLUMN CONF_GALLERY.UPDATE_TIME IS '更新时间';
COMMENT ON COLUMN CONF_GALLERY.DEL_FLAG IS '删除标记;0 不删除 1 删除';

-- 会务图文表
DROP TABLE IF EXISTS CONF_GALLERY_IMG;
CREATE TABLE CONF_GALLERY_IMG(
                                 ID BIGINT NOT NULL,
                                 GALLERY_ID BIGINT,
                                 CONF_ID BIGINT,
                                 IMG_CAT VARCHAR(255),
                                 IMG_URL VARCHAR(255),
                                 CREATE_BY VARCHAR(36),
                                 CREATE_TIME DATETIME,
                                 UPDATE_BY VARCHAR(36),
                                 UPDATE_TIME DATETIME,
                                 DEL_FLAG VARCHAR(1) NOT NULL DEFAULT  0,
                                 PRIMARY KEY (ID)
);

COMMENT ON TABLE CONF_GALLERY_IMG IS '会务图文表';
COMMENT ON COLUMN CONF_GALLERY_IMG.ID IS '主键';
COMMENT ON COLUMN CONF_GALLERY_IMG.GALLERY_ID IS '目录id';
COMMENT ON COLUMN CONF_GALLERY_IMG.CONF_ID IS '会务id';
COMMENT ON COLUMN CONF_GALLERY_IMG.IMG_CAT IS '图片分类';
COMMENT ON COLUMN CONF_GALLERY_IMG.IMG_URL IS '图片路径';
COMMENT ON COLUMN CONF_GALLERY_IMG.CREATE_BY IS '创建人';
COMMENT ON COLUMN CONF_GALLERY_IMG.CREATE_TIME IS '创建时间';
COMMENT ON COLUMN CONF_GALLERY_IMG.UPDATE_BY IS '更新人';
COMMENT ON COLUMN CONF_GALLERY_IMG.UPDATE_TIME IS '更新时间';
COMMENT ON COLUMN CONF_GALLERY_IMG.DEL_FLAG IS '删除标记;0 不删除 1 删除';


-- 会务通知
DROP TABLE IF EXISTS CONF_NOTICE;
CREATE TABLE CONF_NOTICE(
                            ID BIGINT NOT NULL,
                            CONF_ID BIGINT,
                            TITLE VARCHAR(255),
                            CONTENT TEXT,
                            FILE_URL VARCHAR(255),
                            STATUS VARCHAR(2),
                            CREATE_BY VARCHAR(36),
                            CREATE_TIME DATETIME,
                            UPDATE_BY VARCHAR(36),
                            UPDATE_TIME DATETIME,
                            DEL_FLAG CHAR(1) NOT NULL DEFAULT  0,
                            PRIMARY KEY (ID)
);

COMMENT ON TABLE CONF_NOTICE IS '会务通知';
COMMENT ON COLUMN CONF_NOTICE.ID IS '主键';
COMMENT ON COLUMN CONF_NOTICE.CONF_ID IS '会务id';
COMMENT ON COLUMN CONF_NOTICE.TITLE IS '标题';
COMMENT ON COLUMN CONF_NOTICE.CONTENT IS '内容';
COMMENT ON COLUMN CONF_NOTICE.FILE_URL IS '文件路径';
COMMENT ON COLUMN CONF_NOTICE.STATUS IS '状态;0 草稿 1 发布';
COMMENT ON COLUMN CONF_NOTICE.CREATE_BY IS '创建人';
COMMENT ON COLUMN CONF_NOTICE.CREATE_TIME IS '创建时间';
COMMENT ON COLUMN CONF_NOTICE.UPDATE_BY IS '更新人';
COMMENT ON COLUMN CONF_NOTICE.UPDATE_TIME IS '更新时间';
COMMENT ON COLUMN CONF_NOTICE.DEL_FLAG IS '删除标记;0 不删除 1 删除';

-- 会务模块内容资源表
DROP TABLE IF EXISTS CONF_MODULE_RES;
CREATE TABLE CONF_MODULE_RES(
                                ID BIGINT NOT NULL,
                                CONF_ID BIGINT,
                                DIR_ID BIGINT,
                                MODULE_TYPE VARCHAR(255),
                                RES_TYPE VARCHAR(2),
                                DIR_TITLE VARCHAR(255),
                                DIR_TITLE_COLOR_CLASS VARCHAR(255),
                                DIR_BG_URL VARCHAR(255),
                                DIR_BG_COLOR_CLASS VARCHAR(255),
                                FILE_NAME VARCHAR(255),
                                FILE_URL VARCHAR(255),
                                FILE_TYPE VARCHAR(255),
                                SORT INT,
                                CREATE_BY VARCHAR(36),
                                CREATE_TIME DATETIME,
                                UPDATE_BY VARCHAR(36),
                                UPDATE_TIME DATETIME,
                                DEL_FLAG CHAR(1) NOT NULL DEFAULT  0,
                                PRIMARY KEY (ID)
);

COMMENT ON TABLE CONF_MODULE_RES IS '会务模块内容资源表';
COMMENT ON COLUMN CONF_MODULE_RES.ID IS '主键';
COMMENT ON COLUMN CONF_MODULE_RES.CONF_ID IS '会务id';
COMMENT ON COLUMN CONF_MODULE_RES.DIR_ID IS '目录id';
COMMENT ON COLUMN CONF_MODULE_RES.MODULE_TYPE IS '模块类型';
COMMENT ON COLUMN CONF_MODULE_RES.RES_TYPE IS '资源类型;D 目录 F 文件';
COMMENT ON COLUMN CONF_MODULE_RES.DIR_TITLE IS '目录名称';
COMMENT ON COLUMN CONF_MODULE_RES.DIR_TITLE_COLOR_CLASS IS '目录文字颜色';
COMMENT ON COLUMN CONF_MODULE_RES.DIR_BG_URL IS '目录底图图片地址';
COMMENT ON COLUMN CONF_MODULE_RES.DIR_BG_COLOR_CLASS IS '目录背景颜色';
COMMENT ON COLUMN CONF_MODULE_RES.FILE_NAME IS '资源名称';
COMMENT ON COLUMN CONF_MODULE_RES.FILE_URL IS '资源地址';
COMMENT ON COLUMN CONF_MODULE_RES.FILE_TYPE IS '资源类型;资源后缀';
COMMENT ON COLUMN CONF_MODULE_RES.SORT IS '排序';
COMMENT ON COLUMN CONF_MODULE_RES.CREATE_BY IS '创建人';
COMMENT ON COLUMN CONF_MODULE_RES.CREATE_TIME IS '创建时间';
COMMENT ON COLUMN CONF_MODULE_RES.UPDATE_BY IS '更新人';
COMMENT ON COLUMN CONF_MODULE_RES.UPDATE_TIME IS '更新时间';
COMMENT ON COLUMN CONF_MODULE_RES.DEL_FLAG IS '删除标记;0 不删除 1 删除';






