package com.ruoyi.conference.domain.dto

import com.baomidou.mybatisplus.annotation.IdType
import com.baomidou.mybatisplus.annotation.TableId
import com.baomidou.mybatisplus.annotation.TableName
import com.ruoyi.common.core.domain.BaseEntity
import com.ruoyi.conference.domain.BaseConfEntity
import jakarta.validation.constraints.NotBlank
import jakarta.validation.constraints.NotNull
import java.util.Date

/**
 * 会务模块内容资源文件
 */
data class ConfModuleFileResDto(

    /** 主键 */
    var id: Long? = null,

    /** 会务id */
    @field:NotNull(message = "会务id不能为空")
    var confId: Long? = null,

    /** 目录id */
    var dirId: Long? = null,

    /** 模块类型 */
    @field:NotBlank(message = "模块类型不能为空")
    var moduleType: String? = null,

    /** 资源名称 */
    @field:NotBlank(message = "资源名称不能为空")
    var fileName: String? = null,

    /** 资源地址 */
    @field:NotBlank(message = "资源地址不能为空")
    var fileUrl: String? = null,

    /** 资源类型;资源后缀 */
    @field:NotBlank(message = "资源类型不能为空")
    var fileType: String? = null,

    /** 排序 */
    var sort: Int? = null,

)
