package com.ruoyi.conference.controller

import com.ruoyi.common.core.controller.BaseController
import com.ruoyi.common.core.domain.AjaxResult
import com.ruoyi.common.core.page.TableDataInfo
import com.ruoyi.conference.domain.dto.ConfModuleDirResDto
import com.ruoyi.conference.domain.dto.ConfModuleFileResDto
import com.ruoyi.conference.domain.vo.ConfModuleResVo
import com.ruoyi.conference.service.ConfModuleResService
import org.springframework.validation.annotation.Validated
import org.springframework.web.bind.annotation.DeleteMapping
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.PathVariable
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController

/**
 * 会务模块内容资源 Controller
 */
@RestController
@RequestMapping("/conference/module/res")
@Validated
open class ConfModuleResController(
    private val confModuleResService: ConfModuleResService
): BaseController() {

    /**
     * 获取会务模块内容目录资源
     */
    @GetMapping("dir/list")
    open fun listModuleDirRes(confId: Long, moduleType: String): AjaxResult {
        val list = confModuleResService.listModuleDirRes(confId, moduleType)
        return AjaxResult.success(list)
    }

    /**
     * 获取会务模块内容文件资源
     */
    @GetMapping("file/list")
    open fun listModuleFileResPage(confId: Long, moduleType: String, dirId: Long? = null): AjaxResult {
        val list = confModuleResService.listModuleFileRes(confId, moduleType, dirId)
        return AjaxResult.success(list)
    }

    /**
     * 获取会务模块内容文件资源
     */
    @GetMapping("/{id}")
    open fun getModuleRes(@PathVariable id: Long): AjaxResult {
        val vo = confModuleResService.getModuleRes(id)
        return AjaxResult.success(vo)
    }

    /**
     * 保存会务模块内容目录资源
     */
    @PostMapping("dir/save")
    open fun saveModuleDirRes(@RequestBody @Validated dto: ConfModuleDirResDto): AjaxResult {
        confModuleResService.saveModuleDirRes(dto)
        return AjaxResult.success()
    }

    /**
     * 保存会务模块内容文件资源
     */
    @PostMapping("file/save")
    open fun saveModuleFileRes(@RequestBody @Validated dto: ConfModuleFileResDto): AjaxResult {
        confModuleResService.saveModuleFileRes(dto)
        return AjaxResult.success()
    }

    /**
     * 删除会务模块内容资源
     */
    @DeleteMapping("/{id}")
    open fun delModuleRes(@PathVariable id: Long): AjaxResult{
        confModuleResService.delModuleRes(id)
        return AjaxResult.success()
    }

}