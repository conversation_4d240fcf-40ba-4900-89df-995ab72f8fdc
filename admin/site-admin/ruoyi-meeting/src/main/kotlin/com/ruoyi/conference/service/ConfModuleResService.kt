package com.ruoyi.conference.service

import com.github.yulichang.base.MPJBaseServiceImpl
import com.ruoyi.common.exception.ServiceException
import com.ruoyi.conference.domain.BaseConfEntity
import com.ruoyi.conference.domain.ConfModuleRes
import com.ruoyi.conference.domain.dto.ConfModuleDirResDto
import com.ruoyi.conference.domain.dto.ConfModuleFileResDto
import com.ruoyi.conference.domain.enums.ConfModuleResType
import com.ruoyi.conference.domain.mapper.ConfModuleResConvertMapper
import com.ruoyi.conference.domain.vo.ConfModuleResVo
import com.ruoyi.conference.mapper.ConfBasicMapper
import com.ruoyi.conference.mapper.ConfModuleResMapper
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional

/**
 * 会务模块内容资源 Service
 */
@Service
@Transactional(rollbackFor = [Throwable::class])
open class ConfModuleResService(
    private val confBasicMapper: ConfBasicMapper
) :
    MPJBaseServiceImpl<ConfModuleResMapper, ConfModuleRes>(){

    /**
     * 获取会务模块内容资源
     */
    open fun getModuleRes(id: Long): ConfModuleResVo {
        return ConfModuleResConvertMapper.INSTANCE.convertVo(this.getById(id))
    }

    /**
     * 获取会务模块内容目录资源
     */
    open fun listModuleDirRes(confId: Long, moduleType: String): List<ConfModuleResVo> {
        return this.ktQuery()
            .eq(ConfModuleRes::confId, confId)
            .eq(ConfModuleRes::moduleType, moduleType)
            .eq(ConfModuleRes::resType, ConfModuleResType.DIR.code)
            .orderByAsc(ConfModuleRes::sort)
            .list()
            .map(ConfModuleResConvertMapper.INSTANCE::convertVo)
    }

    /**
     * 获取会务模块内容文件资源
     */
    open fun listModuleFileRes(confId: Long, moduleType: String, dirId: Long? = null): List<ConfModuleResVo> {
        return this.ktQuery()
            .eq(ConfModuleRes::confId, confId)
            .eq(ConfModuleRes::moduleType, moduleType)
            .eq(dirId != null, ConfModuleRes::dirId, dirId)
            .eq(ConfModuleRes::resType, ConfModuleResType.FILE.code)
            .orderByAsc(ConfModuleRes::sort)
            .list()
            .map(ConfModuleResConvertMapper.INSTANCE::convertVo)
    }

    /**
     * 保存会务模块内容目录资源
     */
    open fun saveModuleDirRes(dto: ConfModuleDirResDto) {
        val entity = ConfModuleResConvertMapper.INSTANCE.convert(dto)
        confBasicMapper.selectById(entity.confId) ?: throw ServiceException("会务不存在")
        entity.resType = ConfModuleResType.DIR.code
        BaseConfEntity.setProps(entity)
        this.saveOrUpdate(entity)
    }

    /**
     * 保存会务模块内容文件资源
     */
    open fun saveModuleFileRes(dto: ConfModuleFileResDto) {
        val entity = ConfModuleResConvertMapper.INSTANCE.convert(dto)
        confBasicMapper.selectById(entity.confId) ?: throw ServiceException("会务不存在")
        entity.dirId?.let {
            confBasicMapper.selectById(it) ?: throw ServiceException("目录不存在")
        }
        entity.resType = ConfModuleResType.FILE.code
        BaseConfEntity.setProps(entity)
        this.saveOrUpdate(entity)
    }

    /**
     * 删除会务模块内容资源
     */
    open fun delModuleRes(id: Long){
        this.removeById(id)
    }
}
