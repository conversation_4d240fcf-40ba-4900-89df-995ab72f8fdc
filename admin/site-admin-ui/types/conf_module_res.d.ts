interface IConfModuleRes {

  /** 主键 */
  id?: string;

  /** 会务id */
  confId?: string;

  /** 目录id */
  dirId?: string;

  /** 模块类型 */
  moduleType?: string;

  /** 资源类型; D 目录 F 文件 */
  resType?: string;

  /** 目录名称 */
  dirTitle?: string;

  /** 目录文字颜色 */
  dirTitleColorClass?: string;

  /** 目录底图图片地址 */
  dirBgUrl?: string;

  /** 目录背景颜色 */
  dirBgColorClass?: string;

  /** 资源名称 */
  fileName?: string;

  /** 资源地址 */
  fileUrl?: string;

  /** 资源类型; 资源后缀 */
  fileType?: string;

  /** 排序 */
  sort?: number;

  /** 创建人 */
  createBy?: string;

  /** 创建时间 */
  createTime?: string | Date;

  /** 更新人 */
  updateBy?: string;

  /** 更新时间 */
  updateTime?: string | Date;
}