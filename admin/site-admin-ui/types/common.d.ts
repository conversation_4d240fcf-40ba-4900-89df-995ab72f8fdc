/** 接口返回类型 */
interface IResult<T> {
    type: 'iresult'
    code: number,
    data: T,
    msg: string,
    message: string
}

/** 基础内容字段 */
interface IBaseField {
    createBy: string
    createDate: string
    delFlag: string
    id: string
    pageNum: number
    pageCurrent: number
    pageSize: number
    readOnly: false
    remarks: string
    updateBy: string
    updateDate: string
}

/** 分页 */
interface IPage<T> {
    type: 'ipage'
    code: number
    msg: string
    rows: Array<T>
    total: number
}


type PageParams = {
    pageNum: number;
    pageSize: number;
};

type IPageQuery<T> = T & PageParams;

