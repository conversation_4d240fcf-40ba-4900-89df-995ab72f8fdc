interface IConfAddr {

  /** id */
  id?: number;

  /** 会务 id */
  confId?: number;

  /** 描述信息 */
  descInfo?: string;

  /** 会务地址 */
  confAddr?: string;

  /** 定位 id */
  locId?: string;

  /** 定位名称 */
  locName?: string;

  /** 定位经纬度（如 "lng,lat"） */
  loc?: string;

  /** 定位完整地址 */
  locFullAddr?: string;

  /** 创建人 */
  createBy?: string;

  /** 创建时间 */
  createTime?: string | Date;

  /** 更新人 */
  updateBy?: string;

  /** 更新时间 */
  updateTime?: string | Date;
}

// amap poi类型
interface POI {
  id: string;
  name: string;
  type: string;
  location: any; // LngLat
  address: string;
  distance?: number;
  tel?: string;
  website?: string;
  pcode?: string;
  citycode?: string;
  adcode?: string;
  postcode?: string;
  pname?: string;
  cityname?: string;
  adname?: string;
  email?: string;
  entr_location?: any; // LngLat
  exit_location?: any; // LngLat
}