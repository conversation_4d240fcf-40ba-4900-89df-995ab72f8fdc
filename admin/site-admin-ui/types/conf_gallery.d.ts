interface IConfGallery {
  /** 主键 */
  id?: string;
  /** 会务ID */
  confId?: string;
  /** 目录名称 */
  galleryName?: string;
  /** 封面 */
  galleryCover?: string;
  /** 日期 */
  galleryDate?: string;
  /** 地点 */
  galleryLocation?: string;
  /** 排序 */
  sort?: number;
  /** 创建人 */
  createBy?: string;
  /** 创建时间 */
  createTime?: Date;
  /** 更新人 */
  updateBy?: string;
  /** 更新时间 */
  updateTime?: Date;
}

interface IConfGalleryImg {
  /** 主键 */
  id?: string;
  /** 目录id */
  galleryId?: string;
  /** 会务id */
  confId?: string;
  /** 图片分类 */
  imgCat?: string;
  /** 图片路径 */
  imgUrl?: string;
  /** 创建人 */
  createBy?: string;
  /** 创建时间 */
  createTime?: Date;
  /** 更新人 */
  updateBy?: string;
  /** 更新时间 */
  updateTime?: Date;
}

// 批量图片保存对象
interface IConfGalleryImgBatchSave {
  galleryId: string;
  confId: string;
  imgCat: string;
  imgUrls: string[];
}