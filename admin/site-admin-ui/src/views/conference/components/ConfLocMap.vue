<script setup lang="ts">
import AMapLoader from '@amap/amap-jsapi-loader';
import {ref, onMounted, onUnmounted, shallowRef, watch, PropType, nextTick} from 'vue';
import {Location, Close, Position, Plus, Edit, Delete, MapLocation, View, Hide, Setting, Search} from "@element-plus/icons-vue";
import { ElMessage, ElMessageBox } from 'element-plus';

const props = defineProps({
  modelValue: {
    type: Array as PropType<IConfAddr[]>,
    default: () => ([])
  },
  disabled: {
    type: Boolean,
    default: false
  }
})

const emits = defineEmits(['update:modelValue'])

// amap 对象
const AMap:any = shallowRef(null)
// 地图对象
const mapInstance:any = shallowRef(null)
// 地图autoComplete
const autoInstance:any = shallowRef(null)
// 地图搜索
const placeSearch:any = shallowRef(null)
// 存储所有标记点
const markers = ref<any[]>([])
// 地图是否显示
const showMap = ref(true)
// 地图模式：'display' 显示模式，'locate' 定位模式
const mapMode = ref<'display' | 'locate'>('display')
// 当前编辑的地点索引
const editingIndex = ref(-1)

// 表单数据
const formData = ref<Partial<IConfAddr>>({
  confAddr: '',
  descInfo: ''
})

// 判断是否有定位数据
const hasLocation = (addr: IConfAddr): boolean => {
  return !!addr.locId
}

// 检查定位索引是否有效
const isValidSearchingIndex = (): boolean => {
  return searchingForIndex.value >= 0 &&
         searchingForIndex.value < props.modelValue.length
}

// 安全地退出定位模式（检查状态一致性）
const safeExitLocateMode = () => {
  if (mapMode.value === 'locate') {
    exitLocateMode()
  }
}

// 监听 modelValue 变化，更新地图标记
watch(() => props.modelValue, (newValue) => {
    nextTick(() => {
      updateMapMarkers(newValue)
    })
  }, { immediate: true, deep: true })

onMounted(()=>{
  initAMap()
})

onUnmounted(() => {
  // 组件卸载时确保清理所有状态
  if (mapMode.value === 'locate') {
    mapMode.value = 'display'
    searchingForIndex.value = -1
  }
  clearSearch()
  clearAllMarkers()
  mapInstance.value?.destroy();
});

async function initAMap() {
  window._AMapSecurityConfig = {
    serviceHost: window.location.origin + "/_AMapService",
  };
  AMap.value = await AMapLoader.load({
    key: "26f9b73b39a5efd5bb0ecb7e58809bdc",
    version: "2.0",
    plugins: ["AMap.Scale","AMap.ToolBar","AMap.AutoComplete","AMap.PlaceSearch"],
  })
  mapInstance.value = new AMap.value.Map("container", {
    viewMode: "2D",
    zoom: 11,
    resizeEnable: true,
  });
  let scale = new AMap.value.Scale()
  mapInstance.value.addControl(scale);
  autoInstance.value = new AMap.value.AutoComplete({
    city: '上海'
  });
  placeSearch.value = new AMap.value.PlaceSearch({
    map: mapInstance.value,
  });

  // 初始化完成后显示标记
  setTimeout(() => {
    updateMapMarkers(props.modelValue)
  }, 100)
}

// 清除所有标记
const clearAllMarkers = () => {
  mapInstance.value.clearMap()
  markers.value = []
}

// 更新地图标记
const updateMapMarkers = (addresses: IConfAddr[]) => {
  if (!mapInstance.value || !AMap.value || !showMap.value) return

  clearAllMarkers()

  // 只显示有定位数据的地点
  const locatedAddresses = addresses.filter(addr => hasLocation(addr) && addr.loc)

  if (locatedAddresses.length === 0) return

  locatedAddresses.forEach((addr, index) => {
    if (addr.loc) {
      const [lng, lat] = addr.loc.split(',').map(Number)
      if (!isNaN(lng) && !isNaN(lat)) {
        const marker = new AMap.value.Marker({
          position: [lng, lat],
          title: addr.locName || addr.confAddr,
          content: `<div class="custom-marker">
            <div class="marker-icon">📍</div>
            <div class="marker-label">${addr.locName || addr.confAddr}</div>
          </div>`,
          anchor: 'bottom-center',
        })

        // 添加点击事件显示信息窗口
        const infoWindow = new AMap.value.InfoWindow({
          content: `
            <div class="info-window">
              <h4 style="margin: 0 0 8px 0; color: #333;">${addr.locName || addr.confAddr}</h4>
              <p style="margin: 4px 0; color: #666; font-size: 13px;">地址: ${addr.locFullAddr || addr.confAddr}</p>
              ${addr.descInfo ? `<p style="margin: 4px 0; color: #666; font-size: 13px;">描述: ${addr.descInfo}</p>` : ''}
              <div style="margin-top: 8px;${props.disabled ? 'display: none;' : ''}">
                <button id="edit-btn-${addr.confId || index}" style="background: #409EFF; color: white; border: none; padding: 4px 8px; border-radius: 4px; cursor: pointer; font-size: 12px; margin-right: 4px;">重新定位</button>
                <button id="remove-btn-${addr.confId || index}" style="background: #F56C6C; color: white; border: none; padding: 4px 8px; border-radius: 4px; cursor: pointer; font-size: 12px;">删除定位</button>
              </div>
            </div>
          `,
          anchor: 'bottom-center',
          offset: [0,-60]
        })

        marker.on('click', () => {
          infoWindow.open(mapInstance.value, marker.getPosition())

          // 在InfoWindow打开后绑定按钮事件
          setTimeout(() => {
            const editBtn = document.getElementById(`edit-btn-${addr.confId || index}`)
            const removeBtn = document.getElementById(`remove-btn-${addr.confId || index}`)

            if (editBtn) {
              editBtn.onclick = () => {
                infoWindow.close()
                const addrIndex = props.modelValue.findIndex(item => (item.confId || '') === (addr.confId || ''))
                if (addrIndex !== -1) {
                  editLocation(addrIndex)
                }
              }
            }

            if (removeBtn) {
              removeBtn.onclick = () => {
                infoWindow.close()
                const addrIndex = props.modelValue.findIndex(item => (item.confId || '') === (addr.confId || ''))
                if (addrIndex !== -1) {
                  removeLocation(addrIndex)
                }
              }
            }
          }, 100)
        })

        mapInstance.value.add(marker)
        markers.value.push(marker)
      }
    }
  })

  // 调整地图视野
  if (markers.value.length > 1) {
    mapInstance.value.setFitView(markers.value, true, [100,100,100,100])
  } else if (markers.value.length === 1) {
    mapInstance.value.setZoomAndCenter(15, markers.value[0].getPosition())
  }
}

// 搜索相关
const searchLocation = ref('')
const showClearIcon = ref(false)
const placeInfos = ref<POI[]>([])
const showPlaceList = ref(false)
const searchingForIndex = ref(-1) // 当前搜索的是哪个地点

const querySearchAsync = (queryString: string, cb: (arg: any) => void) => {
  if (!queryString) {
    cb([]);
    showClearIcon.value = false;
    return;
  }
  showClearIcon.value = true;
  autoInstance.value.search(queryString, function(status: string, result: any) {
    if (status === 'complete' && result.info === 'OK') {
      cb(result.tips)
    } else {
      cb([])
    }
  });
}

const placeSearchAsync = (item: any) => {
  placeSearch.value.setCity(item.adcode);
  placeSearch.value.search(item.name, (status:any, result:any) => {
    if (status === 'complete' && result.info === 'OK') {
      placeInfos.value = result.poiList.pois
      showPlaceList.value = true
    } else {
      placeInfos.value = []
      showPlaceList.value = false
    }
  });
}

// 清除搜索
const clearSearch = () => {
  searchLocation.value = ''
  placeInfos.value = []
  showPlaceList.value = false
  showClearIcon.value = false
  // 注意：不重置 searchingForIndex，保持定位模式状态
}

// 定位地点
const locatePlace = (poi: POI) => {
  if (!showMap.value) {
    showMap.value = true
    nextTick(() => {
      mapInstance.value.setZoomAndCenter(15, poi.location)
    })
  } else {
    mapInstance.value.setZoomAndCenter(15, poi.location)
  }
}

// 设置定位
const setLocation = (poi: POI) => {
  // 检查索引有效性
  if (!isValidSearchingIndex()) {
    ElMessage.error('定位状态异常，请重新选择地点进行定位')
    safeExitLocateMode()
    return
  }

  const newList = [...props.modelValue]
  const item = newList[searchingForIndex.value]

  // 设置定位数据
  item.locId = poi.id
  item.locName = poi.name
  item.loc = `${poi.location.lng},${poi.location.lat}`
  item.locFullAddr = [poi.pname, poi.cityname, poi.adname, poi.address].filter(Boolean).join(',')

  // 如果没有会务地址，则设置为POI地址
  if (!item.confAddr) {
    item.confAddr = poi.address
  }

  emits('update:modelValue', newList)

  // 添加定位成功后自动退出定位模式
  exitLocateMode()

  // 清除所有标记并重新显示已定位的地点
  nextTick(() => {
    updateMapMarkers(newList)
  })

  ElMessage.success('定位设置成功')
}

// 列表管理功能
const addVenue = () => {
  if (!formData.value.confAddr?.trim()) {
    ElMessage.warning('请填写会务地址')
    return
  }

  // 如果正在定位模式，先退出
  if (mapMode.value === 'locate') {
    exitLocateMode()
  }

  const newVenue: IConfAddr = {
    confId: Date.now(), // 临时ID
    confAddr: formData.value.confAddr.trim(),
    descInfo: formData.value.descInfo?.trim() || '',
  }

  const newList = [...props.modelValue, newVenue]
  emits('update:modelValue', newList)

  // 重置表单
  formData.value = {
    confAddr: '',
    descInfo: ''
  }

  ElMessage.success('添加成功')
}

const editVenue = (index: number) => {
  editingIndex.value = index
  formData.value = {
    confAddr: props.modelValue[index].confAddr,
    descInfo: props.modelValue[index].descInfo
  }
}

const saveEdit = () => {
  if (!formData.value.confAddr?.trim()) {
    ElMessage.warning('请填写会务地址')
    return
  }

  const newList = [...props.modelValue]
  newList[editingIndex.value] = {
    ...newList[editingIndex.value],
    confAddr: formData.value.confAddr!.trim(),
    descInfo: formData.value.descInfo?.trim() || '',
  }

  emits('update:modelValue', newList)
  cancelEdit()

  ElMessage.success('修改成功')
}

const cancelEdit = () => {
  editingIndex.value = -1
  formData.value = {
    confAddr: '',
    descInfo: ''
  }
}

// 表内编辑更新方法
const updateVenue = (index: number, field: string, value: string) => {
  if (!value?.trim() && field === 'confAddr') {
    ElMessage.warning('会务地址不能为空')
    return
  }

  // 如果正在定位模式，且编辑的是正在定位的地点，保持定位状态
  // 其他情况下不需要特殊处理，因为只是更新文本信息

  const newList = [...props.modelValue]
  newList[index] = {
    ...newList[index],
    [field]: value?.trim() || '',
  }

  emits('update:modelValue', newList)
}

const removeVenue = (index: number) => {
  ElMessageBox.confirm('确定要删除这个会务地点吗？', '确认删除', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    // 如果正在定位模式，且删除的是正在定位的地点，需要退出定位模式
    if (mapMode.value === 'locate' && searchingForIndex.value === index) {
      exitLocateMode()
    } else if (mapMode.value === 'locate' && searchingForIndex.value > index) {
      // 如果删除的地点在正在定位的地点之前，需要调整索引
      searchingForIndex.value = searchingForIndex.value - 1
    } else if (mapMode.value === 'locate') {
      // 其他情况下也退出定位模式，避免索引混乱
      exitLocateMode()
    }

    const newList = [...props.modelValue]
    newList.splice(index, 1)
    emits('update:modelValue', newList)
    ElMessage.success('删除成功')
  }).catch(() => {
    // 用户取消
  })
}

const editLocation = (index: number) => {
  searchingForIndex.value = index
  mapMode.value = 'locate'
  clearSearch()

  // 进入定位模式时清除所有已定位的标记
  clearAllMarkers()

  if (!showMap.value) {
    showMap.value = true
  }
  ElMessage.info('已进入定位模式，请搜索并选择地点进行定位')
}

// 退出定位模式
const exitLocateMode = () => {
  mapMode.value = 'display'
  searchingForIndex.value = -1
  clearSearch()

  // 清除所有标记并重新显示已定位的地点
  nextTick(() => {
    updateMapMarkers(props.modelValue)
  })

  ElMessage.success('已退出定位模式')
}

const removeLocation = (index: number) => {
  // 如果正在定位模式，且清除的是正在定位的地点，需要退出定位模式
  if (mapMode.value === 'locate' && searchingForIndex.value === index) {
    exitLocateMode()
  } else if (mapMode.value === 'locate') {
    // 其他情况下也退出定位模式，避免状态不一致
    exitLocateMode()
  }

  const newList = [...props.modelValue]
  const item = newList[index]

  // 清除定位相关数据
  delete item.locId
  delete item.locName
  delete item.loc
  delete item.locFullAddr

  emits('update:modelValue', newList)
  ElMessage.success('定位数据已清除')
}

// 切换地图显示
const toggleMap = () => {
  showMap.value = !showMap.value
  if (showMap.value) {
    nextTick(() => {
      updateMapMarkers(props.modelValue)
    })
  } else {
    // 隐藏地图时退出定位模式
    if (mapMode.value === 'locate') {
      exitLocateMode()
    }
  }
}
</script>

<template>
  <div class="venue-manager">
    <!-- 头部操作栏 -->
<!--    <div class="mb-4 flex items-center justify-between bg-white p-4 rounded-lg shadow">-->
<!--      <div>-->
<!--        <h3 class="text-lg font-medium text-gray-800 m-0">会议地点管理</h3>-->
<!--        <p class="text-sm text-gray-500 mt-1">-->
<!--          左侧表格支持直接编辑会议信息，右侧地图支持两种模式：显示模式查看所有定位，定位模式设置地点位置-->
<!--        </p>-->
<!--      </div>-->
<!--      <div class="flex items-center gap-3">-->
<!--        <div class="text-sm text-gray-600">-->
<!--          <div>共 {{ modelValue.length }} 个地点</div>-->
<!--          <div>已定位 {{ modelValue.filter(item => hasLocation(item)).length }} 个</div>-->
<!--        </div>-->
<!--        <el-button @click="toggleMap" :type="showMap ? 'primary' : 'default'" :icon="showMap ? View : Hide">-->
<!--          {{ showMap ? '隐藏地图' : '显示地图' }}-->
<!--        </el-button>-->
<!--      </div>-->
<!--    </div>-->

    <div class="grid grid-cols-12 gap-4">
      <!-- 左侧：会议列表区域 -->
      <div class="col-span-5">
        <!-- 添加新地点表单 -->
        <div class="bg-white rounded-lg shadow p-4 mb-4" v-if="!disabled">
          <h4 class="text-base font-medium text-gray-800 mb-3">
            {{ editingIndex >= 0 ? '编辑会议地点' : '添加会议地点' }}
          </h4>
          <div class="space-y-3">
            <div>
              <label class="block text-sm text-gray-700 mb-1">会务地址 *</label>
              <el-input
                  v-model="formData.confAddr"
                  placeholder="请输入会务地址"
                  clearable
              />
            </div>
            <div>
              <label class="block text-sm text-gray-700 mb-1">描述信息</label>
              <el-input
                  v-model="formData.descInfo"
                  type="textarea"
                  :rows="2"
                  placeholder="请输入描述信息"
              />
            </div>
            <div class="flex gap-2">
              <el-button
                  v-if="editingIndex >= 0"
                  @click="saveEdit"
                  type="primary"
                  :icon="Edit"
              >
                保存修改
              </el-button>
              <el-button
                  v-else
                  @click="addVenue"
                  type="primary"
                  :icon="Plus"
              >
                添加地点
              </el-button>
              <el-button
                  v-if="editingIndex >= 0"
                  @click="cancelEdit"
              >
                取消
              </el-button>
            </div>
          </div>
        </div>

        <!-- 会议地点列表 -->
        <div class="bg-white rounded-lg shadow">
          <div class="px-4 py-3 border-b border-gray-200 bg-gray-50 rounded-t-lg flex items-center justify-between">
            <div>
              <span class="font-medium text-gray-800">会议地点列表</span>
            </div>
            <div class="text-sm text-gray-500">
              共 {{ modelValue.length }} 个地点
            </div>
          </div>
          <div class="max-h-96 overflow-y-auto">
            <div v-if="modelValue.length === 0" class="p-8 text-center text-gray-500">
              <div class="text-4xl mb-2">📍</div>
              <div>暂无会议地点数据</div>
              <div class="text-xs mt-1">请先添加会议地点</div>
            </div>

            <!-- 地点卡片列表 -->
            <div v-else class="p-2 space-y-2">
              <div
                v-for="(item, index) in modelValue"
                :key="item.confId || index"
                class="border border-gray-200 rounded-lg p-3 hover:shadow-md transition-shadow"
                :class="{ 'ring-2 ring-blue-200 bg-blue-50': editingIndex === index }"
              >
                <!-- 地点信息区域 -->
                <div class="mb-3">
                  <div class="flex items-start justify-between mb-2">
                    <div class="flex-1 mr-2">
                      <!-- 会务地址 -->
                      <div class="mb-2">
                        <label class="block text-xs text-gray-500 mb-1">会务地址</label>
                        <el-input
                          v-model="item.confAddr"
                          :disabled="disabled"
                          placeholder="点击输入会务地址"
                          size="small"
                          @blur="updateVenue(index, 'confAddr', item.confAddr)"
                          class="venue-input"
                        />
                      </div>
                      <!-- 描述信息 -->
                      <div>
                        <label class="block text-xs text-gray-500 mb-1">描述信息</label>
                        <el-input
                          v-model="item.descInfo"
                          :disabled="disabled"
                          placeholder="点击输入描述信息"
                          size="small"
                          type="textarea"
                          :rows="2"
                          @blur="updateVenue(index, 'descInfo', item.descInfo)"
                          class="venue-input"
                        />
                      </div>
                    </div>

                    <!-- 定位状态 -->
                    <div class="flex flex-col items-center ml-2">
                      <div
                        v-if="hasLocation(item)"
                        class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800 mb-1"
                      >
                        <el-icon class="mr-1"><MapLocation /></el-icon>
                        已定位
                      </div>
                      <div
                        v-else
                        class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-600 mb-1"
                      >
                        <el-icon class="mr-1"><Location /></el-icon>
                        未定位
                      </div>
                    </div>
                  </div>

                  <!-- 定位详情 -->
                  <div v-if="hasLocation(item)" class="bg-gray-50 rounded p-2 text-xs text-gray-600">
                    <div class="flex items-center gap-1 mb-1">
                      <el-icon class="text-blue-500"><MapLocation /></el-icon>
                      <span class="font-medium">{{ item.locName }}</span>
                    </div>
                    <div class="text-gray-500">{{ item.locFullAddr }}</div>
                  </div>
                </div>

                <!-- 操作按钮区域 -->
                <div class="flex items-center justify-between pt-2 border-t border-gray-100" v-if="!disabled">
                  <div class="flex gap-1">
                    <!-- 定位操作 -->
                    <el-button
                      v-if="hasLocation(item)"
                      @click="editLocation(index)"
                      type="warning"
                      size="small"
                      :icon="Setting"
                      plain
                    >
                      {{searchingForIndex === index ? '正在定位...' : '重新定位'}}
                    </el-button>
                    <el-button
                      v-else
                      @click="editLocation(index)"
                      type="success"
                      size="small"
                      :icon="MapLocation"
                      plain
                    >
                      {{searchingForIndex === index ? '正在定位...' : '点击定位'}}
                    </el-button>
                  </div>

                  <div class="flex gap-1">
                    <!-- 管理操作 -->
                    <el-button
                      v-if="hasLocation(item)"
                      @click="removeLocation(index)"
                      type="info"
                      size="small"
                      :icon="Close"
                      plain
                    >
                      清除定位
                    </el-button>
                    <el-button
                      @click="removeVenue(index)"
                      type="danger"
                      size="small"
                      :icon="Delete"
                      plain
                    >
                      删除
                    </el-button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 右侧：地图区域 -->
      <div class="col-span-7" v-show="showMap">
        <div class="relative bg-white rounded-lg shadow overflow-hidden">
          <!-- 地图容器 -->
          <div id="container"></div>

          <!-- 地图模式指示器和控制 -->
          <div class="absolute top-4 right-4 z-10">
            <div class="bg-white rounded-md shadow-lg px-3 py-2 text-sm">
              <div class="flex items-center gap-2 mb-2">
                <span class="font-medium">
                  {{ mapMode === 'locate' ? '🎯 定位模式' : '📍 显示模式' }}
                </span>
                <el-button
                  v-if="mapMode === 'locate'"
                  @click="exitLocateMode"
                  type="danger"
                  size="small"
                  :icon="Close"
                  plain
                >
                  退出定位
                </el-button>
              </div>
              <div class="text-xs text-gray-500">
                <span v-if="mapMode === 'locate' && searchingForIndex >= 0">
                  正在为"{{ modelValue[searchingForIndex]?.confAddr }}"设置定位
                </span>
                <span v-else>
                  已定位: {{ modelValue.filter(item => hasLocation(item)).length }} 个地点
                </span>
              </div>
            </div>
          </div>

          <!-- 搜索框 (仅在定位模式下显示) -->
          <div v-if="mapMode === 'locate'" class="absolute top-4 left-4 z-10 w-80">
            <div class="flex items-center bg-white rounded-md shadow-lg p-1">
              <el-autocomplete
                  v-model="searchLocation"
                  :fetch-suggestions="querySearchAsync"
                  value-key="name"
                  @select="placeSearchAsync"
                  placeholder="搜索地点进行定位"
                  class="flex-1"
              >
                <template #prefix>
                  <el-icon class="text-blue-500"><Search /></el-icon>
                </template>
                <template #default="{ item }">
                  <div class="flex items-center gap-2 py-1">
                    <el-icon class="text-blue-500 text-base"><Location /></el-icon>
                    <div class="flex flex-col gap-0.5">
                      <span class="font-medium text-gray-800">{{ item.name }}</span>
                      <span class="text-xs text-gray-400">{{ item.district }}</span>
                    </div>
                  </div>
                </template>
              </el-autocomplete>
              <el-button
                  v-if="showClearIcon"
                  @click="clearSearch"
                  class="ml-2 text-gray-400 hover:text-red-400 p-1"
                  type="text"
                  :icon="Close"
                  size="small"
              />
            </div>

            <!-- 搜索结果 (仅在定位模式下显示) -->
            <div v-if="mapMode === 'locate' && showPlaceList && placeInfos.length > 0" class="mt-2 bg-white rounded-md shadow-lg max-h-80 overflow-y-auto">
              <div class="px-4 py-2 border-b border-gray-200 font-medium text-gray-800 bg-gray-50">
                <span>搜索结果 ({{ placeInfos.length }})</span>
                <span v-if="searchingForIndex >= 0" class="text-sm text-blue-600 ml-2">
                  - 为"{{ modelValue[searchingForIndex]?.confAddr }}"设置定位
                </span>
              </div>
              <div class="py-1">
                <div
                    v-for="item in placeInfos"
                    :key="item.id"
                    class="px-4 py-2 border-b border-gray-100 hover:bg-gray-50 transition-colors last:border-b-0"
                >
                  <h4 class="text-sm font-medium text-gray-800 m-0 mb-1">{{ item.name }}</h4>
                  <p v-if="item.address" class="text-xs text-gray-600 mb-2">{{ item.address }}</p>
                  <div class="flex gap-1">
                    <el-button
                        @click="locatePlace(item)"
                        type="primary"
                        size="small"
                        :icon="Position"
                        plain
                    >
                      移动到此位置
                    </el-button>
                    <el-button
                        v-if="searchingForIndex >= 0"
                        @click="setLocation(item)"
                        type="success"
                        size="small"
                        :icon="Plus"
                    >
                      设置定位
                    </el-button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
#container {
  width: 100%;
  height: 600px;
  position: relative;
}

/* 搜索建议下拉框宽度适配 */
:deep(.el-autocomplete-suggestion) {
  width: 320px !important;
  min-width: 320px !important;
}

:deep(.el-popper.is-light) {
  width: 320px !important;
  min-width: 320px !important;
}

/* 自定义标记样式 */
:deep(.custom-marker) {
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
}

:deep(.marker-icon) {
  font-size: 24px;
  filter: drop-shadow(0 2px 4px rgba(0,0,0,0.3));
  color: #10B981;
}

:deep(.marker-label) {
  background: white;
  padding: 2px 8px;
  border-radius: 4px;
  font-size: 12px;
  margin-top: 4px;
  box-shadow: 0 1px 3px rgba(0,0,0,0.3);
  white-space: nowrap;
  max-width: 120px;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* 移除了 marker-delete 样式，因为已定位标记不再显示删除按钮 */

:deep(.info-window) {
  padding: 8px;
  min-width: 200px;
}

/* 响应式布局 */
.venue-manager .col-span-5 {
  transition: all 0.3s ease;
}

.venue-manager[show-map="false"] .col-span-5 {
  grid-column: span 12;
}

/* 小屏幕适配 */
@media (max-width: 1024px) {
  .venue-manager .grid-cols-12 {
    grid-template-columns: 1fr;
  }

  .venue-manager .col-span-5,
  .venue-manager .col-span-7 {
    grid-column: span 12;
  }
}

/* 地点卡片样式优化 */
.venue-input :deep(.el-input__wrapper) {
  border: 1px solid transparent;
  transition: all 0.2s ease;
}

.venue-input :deep(.el-input__wrapper:hover) {
  border-color: var(--el-border-color-hover);
  background-color: #fafafa;
}

.venue-input :deep(.el-input__wrapper.is-focus) {
  border-color: var(--el-color-primary);
  background-color: white;
  box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.1);
}

.venue-input :deep(.el-textarea__inner) {
  border: 1px solid transparent;
  transition: all 0.2s ease;
}

.venue-input :deep(.el-textarea__inner:hover) {
  border-color: var(--el-border-color-hover);
  background-color: #fafafa;
}

.venue-input :deep(.el-textarea__inner:focus) {
  border-color: var(--el-color-primary);
  background-color: white;
  box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.1);
}

/* 卡片悬停效果 */
.venue-manager .space-y-2 > div:hover {
  transform: translateY(-1px);
}

/* 状态标签样式 */
.venue-manager .rounded-full {
  font-weight: 500;
  letter-spacing: 0.025em;
}

/* 输入框样式 */
:deep(.el-input__wrapper) {
  border-radius: 6px;
}

:deep(.el-textarea__inner) {
  border-radius: 6px;
}

/* 按钮组样式 */
:deep(.el-button + .el-button) {
  margin-left: 4px;
}

/* 列表项悬停效果 */
.venue-manager .bg-white > div > div:hover {
  background-color: #f8fafc;
}
</style>