<script setup lang="ts">
import {PropType} from "vue";
import type { CSSProperties } from 'vue'

const imgHost = import.meta.env.VITE_APP_BASE_API


const props = defineProps({
  module: {
    type: Object as PropType<IConfModuleData>,
    required: true
  }
})

const getModuleBackgroundClass = (module: IConfModuleData) => {
  return module.bgImgUrl ? '' : module.bgColorClass;
};

const getWidthClass = (width?: string): string => {
  return width === 'wide' ? 'col-span-2' : 'col-span-1';
};

const getHeightClass = (height?: string): string => {
  return height === '2x' ? 'row-span-2' : 'row-span-1';
};

const getHeightPx = (height?: string): string => {
  return height === '2x' ? 'h-[212px]' : 'h-[100px]';
};

const getModuleClass = (module: IConfModuleData) => {
  return `${getWidthClass(module.width)} ${getHeightClass(module.height)} ${getHeightPx(module.height)} ${getModuleBackgroundClass(module)}`;
};


// 获取模块背景样式
const getModuleBackgroundStyle = (module: IConfModuleData): CSSProperties => {
  if (module.bgImgUrl) {
    return {
      backgroundImage: `url(${imgHost}${module.bgImgUrl})`,
      backgroundSize: 'cover',
      backgroundPosition: 'center',
      backgroundRepeat: 'no-repeat'
    };
  }
  return {};
};
</script>

<template>
    <div
        :class="getModuleClass(module)"
        :style="getModuleBackgroundStyle(module)"
        class="flex relative justify-between p-3 text-white rounded-xl shadow-lg"
    >
      <!-- 背景遮罩层（当使用背景图时） -->
      <div v-if="module.bgImgUrl" class="absolute inset-0 rounded-xl bg-black/30"></div>

      <div class="relative flex-1 pr-2 min-w-0 z-5">
        <h3 class="mb-1 text-sm font-semibold">{{ module.title }}</h3>
        <p v-if="module.subtitle" class="text-xs text-white/80">{{ module.subtitle }}</p>
      </div>
      <div class="absolute right-3 bottom-3 z-5" v-if="module.iconClass">
        <div class="p-1.5 rounded-full bg-white/20 preview-icon">
          <div :class="module.iconClass"></div>
        </div>
      </div>
    </div>
</template>

<style scoped>

</style>