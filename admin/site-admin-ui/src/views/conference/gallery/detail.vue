<script setup lang="ts">
import {useRoute} from "vue-router";
import {onMounted, ref, watch, nextTick, computed} from "vue";
import {getConfBasic} from "@/api/conference/basic";
import {
  listGallery, pageGalleryImg, saveGallery,
  saveGalleryImg,
  saveGalleryImgBatch,
  delGallery,
  delGalleryImg,
  listGalleryImgCat
} from "@/api/conference/gallery";
// import ImageUpload from "@/components/ImageUpload/index.vue";
// import Pagination from "@/components/Pagination/index.vue";
import {ElMessage, ElMessageBox} from 'element-plus';
import {Plus, Edit, Delete, Picture, Calendar, Location, Document, Flag, Upload, Loading} from '@element-plus/icons-vue';
import View from "@/views/company/service/train/view.vue";

// 批量图片保存对象
interface IConfGalleryImgBatchSave {
  galleryId: string;
  confId: string;
  imgCat: string;
  imgUrls: string[];
}

defineOptions({
  name: "GalleryDetail",
})
const route = useRoute();
const confId = ref<string>('')
const confBasic = ref<IConfBasic>()
const galleries = ref<IConfGallery[]>([])
const galleryImgs = ref<IConfGalleryImg[]>([])
const imgHost = import.meta.env.VITE_APP_BASE_API
const currentGalleryId = ref<string>('')

// 对话框相关
const galleryDialogVisible = ref(false)
const galleryForm = ref<IConfGallery>({})
const galleryFormRef = ref()

const imgDialogVisible = ref(false)
const imgForm = ref<IConfGalleryImg>({})
const imgFormRef = ref()

// 批量上传相关
const batchImgDialogVisible = ref(false)
const batchImgListStr = ref<string>('')
const batchImgCat = ref('')

// 获取相册列表
const getGalleryList = async (confId: string) => {
  const {data} = await listGallery({confId})
  galleries.value = data
  if (data.length > 0 && !currentGalleryId.value) {
    currentGalleryId.value = data[0].id!
  }
}

// 获取会务信息
const getConf = async (confId: string) => {
  const {data} = await getConfBasic(confId)
  confBasic.value = data
}

const imgCats = ref<string[]>([])
const getImgCats = async () => {
  if (!currentGalleryId.value) return
  const {data} = await listGalleryImgCat(currentGalleryId.value)
  imgCats.value = data
}

// 图片搜索表单
const searchForm = ref<IPageQuery<Partial<IConfGalleryImg>>>({
  galleryId: '',
  imgCat: '',
  pageNum: 1,
  pageSize: 20,
})
const total = ref(0)
const loading = ref(false)
const noMore = ref(false)

// 获取图片列表
const getGalleryImgList = async (isLoadMore = false) => {
  if (!currentGalleryId.value || loading.value) return

  loading.value = true
  try {
    searchForm.value.galleryId = currentGalleryId.value
    const data = await pageGalleryImg(searchForm.value)

    if (isLoadMore) {
      // 加载更多时追加数据
      galleryImgs.value = [...galleryImgs.value, ...data.rows]
    } else {
      // 首次加载或刷新时替换数据
      galleryImgs.value = data.rows
    }

    total.value = data.total
    noMore.value = galleryImgs.value.length >= data.total
  } catch (error) {
    console.error('获取图片列表失败:', error)
  } finally {
    loading.value = false
  }
}

// 加载更多图片
const loadMore = async () => {
  if (loading.value || noMore.value) return
  searchForm.value.pageNum++
  await getGalleryImgList(true)
}

// 选择相册
const selectGallery = (galleryId: string) => {
  searchForm.value.pageNum = 1
  searchForm.value.imgCat = ''
  noMore.value = false
  galleryImgs.value = []
  currentGalleryId.value = galleryId
}

// 添加相册
const addGallery = () => {
  galleryForm.value = {
    confId: confId.value,
    galleryName: '',
    galleryCover: '',
    galleryDate: '',
    galleryLocation: '',
    sort: 0
  }
  galleryDialogVisible.value = true
}

// 编辑相册
const editGallery = (gallery: IConfGallery) => {
  galleryForm.value = {...gallery}
  galleryDialogVisible.value = true
}

// 删除相册
const deleteGallery = async (gallery: IConfGallery) => {
  try {
    await ElMessageBox.confirm('确定要删除该相册吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    })
    await delGallery(gallery.id!)
    ElMessage.success('删除成功')
    await getGalleryList(confId.value)
    if (currentGalleryId.value === gallery.id) {
      currentGalleryId.value = galleries.value[0]?.id || ''
    }
  } catch (error) {
    // 用户取消删除
  }
}

// 保存相册
const saveGalleryForm = async () => {
  try {
    await galleryFormRef.value?.validate()
    await saveGallery(galleryForm.value)
    ElMessage.success('保存成功')
    galleryDialogVisible.value = false
    await getGalleryList(confId.value)
  } catch (error) {
    console.error('保存失败:', error)
  }
}

watch(() => route.query.confId, (val) => {
  if (val) {
    confId.value = String(val ?? '')
    getConf(confId.value)
    getGalleryList(confId.value)
  }
}, {immediate: true})

watch(() => currentGalleryId.value, () => {
  if (currentGalleryId.value) {
    getImgCats()
    getGalleryImgList()
  }
})

const handleTabChange = () => {
  searchForm.value.pageNum = 1
  noMore.value = false
  galleryImgs.value = []
  getGalleryImgList()
}

// 添加图片
const addImg = () => {
  imgForm.value = {
    galleryId: currentGalleryId.value,
    confId: confId.value,
    imgCat: '',
    imgUrl: ''
  }
  imgDialogVisible.value = true
}

// 编辑图片
const editImg = (img: IConfGalleryImg) => {
  imgForm.value = {...img}
  imgDialogVisible.value = true
}

// 删除图片
const deleteImg = async (img: IConfGalleryImg) => {
  try {
    await ElMessageBox.confirm('确定要删除该图片吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    })
    await delGalleryImg([img.id!])
    ElMessage.success('删除成功')
    // 重置分页并重新加载
    searchForm.value.pageNum = 1
    noMore.value = false
    galleryImgs.value = []
    await getGalleryImgList()
  } catch (error) {
    // 用户取消删除
  }
}

// 保存图片
const saveImgForm = async () => {
  try {
    await imgFormRef.value?.validate()
    await saveGalleryImg(imgForm.value)
    ElMessage.success('保存成功')
    imgDialogVisible.value = false
    // 重置分页并重新加载
    searchForm.value.pageNum = 1
    noMore.value = false
    galleryImgs.value = []
    getGalleryImgList()
  } catch (error) {
    console.error('保存失败:', error)
  }
}

// 批量添加图片
const batchAddImg = () => {
  batchImgListStr.value = ''
  batchImgCat.value = ''
  batchImgDialogVisible.value = true
}

// 计算批量添加图片列表
const batchAddImgList = computed(() => batchImgListStr.value.split(',').filter(Boolean))

// 批量保存图片
const saveBatchImgForm = async () => {
  if (batchAddImgList.value.length === 0) {
    ElMessage.warning('请至少上传一张图片')
    return
  }

  try {
    const batchData: IConfGalleryImgBatchSave = {
      galleryId: currentGalleryId.value,
      confId: confId.value,
      imgCat: batchImgCat.value,
      imgUrls: batchAddImgList.value
    }

    await saveGalleryImgBatch(batchData)
    ElMessage.success(`成功添加 ${batchData.imgUrls.length} 张图片`)
    batchImgDialogVisible.value = false
    // 重置分页并重新加载
    searchForm.value.pageNum = 1
    noMore.value = false
    galleryImgs.value = []
    getGalleryImgList()
  } catch (error) {
    console.error('批量保存失败:', error)
    ElMessage.error('批量保存失败')
  }
}

// 格式化日期
const formatDate = (date: string | Date) => {
  if (!date) return ''
  const d = new Date(date)
  return d.toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit'
  })
}
</script>

<template>
  <div class="p-4 h-full flex flex-col gap-4 bg-[#f0f2f5]">
    <!-- 主要内容区域 -->
    <div class="flex gap-4 flex-1">
      <!-- 左侧相册列表 -->
      <div class="w-80 flex flex-col">
        <el-card shadow="never" class="h-full">
          <template #header>
            <div class="flex justify-between items-center">
              <span class="text-lg font-medium">{{confBasic?.name}}相册列表</span>
<!--              <el-button type="primary" size="small" @click="addGallery">-->
<!--                <el-icon>-->
<!--                  <Plus/>-->
<!--                </el-icon>-->
<!--                添加相册-->
<!--              </el-button>-->
            </div>
          </template>

          <div class="space-y-3 overflow-y-auto" style="max-height: calc(100vh - 200px);">
            <!-- 添加相册按钮 -->
            <div
                class="border-2 border-dashed border-gray-300 rounded-lg p-4 text-center cursor-pointer hover:border-blue-400 hover:bg-blue-50 transition-colors"
                @click="addGallery"
            >
              <el-icon class="text-2xl text-gray-400 mb-2">
                <Plus/>
              </el-icon>
              <div class="text-gray-500">添加新相册</div>
            </div>

            <!-- 相册列表 -->
            <div
                v-for="gallery in galleries"
                :key="gallery.id"
                class="gallery-item border rounded-lg p-3 cursor-pointer transition-all hover:shadow-md"
                :class="{ 'border-blue-500 bg-blue-50': currentGalleryId === gallery.id, 'border-gray-200': currentGalleryId !== gallery.id }"
                @click="selectGallery(gallery.id!)"
            >
              <div class="flex">
                <!-- 封面图片 -->
                <div class="w-16 h-16 rounded overflow-hidden bg-gray-100 flex-shrink-0">
                  <el-image
                      v-if="gallery.galleryCover"
                      :src="imgHost + gallery.galleryCover"
                      fit="cover"
                      class="w-full h-full"
                  />
                  <div v-else class="w-full h-full flex items-center justify-center text-gray-400">
                    <el-icon>
                      <Picture/>
                    </el-icon>
                  </div>
                </div>

                <!-- 相册信息 -->
                <div class="ml-3 flex-1 min-w-0">
                  <div class="font-medium text-gray-900 truncate">{{ gallery.galleryName }}</div>
                  <div class="text-sm text-gray-500 mt-1">
                    <div v-if="gallery.galleryDate" class="flex items-center">
                      <el-icon class="mr-1">
                        <Calendar/>
                      </el-icon>
                      {{ gallery.galleryDate }}
                    </div>
                    <div v-if="gallery.galleryLocation" class="flex items-center mt-1">
                      <el-icon class="mr-1">
                        <Location/>
                      </el-icon>
                      {{ gallery.galleryLocation }}
                    </div>
                  </div>
                </div>

                <!-- 操作按钮 -->
                <div class="flex flex-col gap-1 ml-2">
                  <el-button
                      type="primary"
                      size="small"
                      text
                      @click.stop="editGallery(gallery)"
                      class="p-1"
                  >
                    <el-icon>
                      <Edit/>
                    </el-icon>
                  </el-button>
                  <el-button
                      type="danger"
                      size="small"
                      text
                      @click.stop="deleteGallery(gallery)"
                      class="p-1"
                  >
                    <el-icon>
                      <Delete/>
                    </el-icon>
                  </el-button>
                </div>
              </div>
            </div>
          </div>
        </el-card>
      </div>

      <!-- 右侧图片展示区域 -->
      <div class="flex-1 flex flex-col">
        <el-card shadow="never" class="h-full">
          <template #header>
            <div class="flex justify-between items-center">
              <span class="text-lg font-medium">图片管理</span>
              <div class="flex gap-2">
                <el-button
                    type="primary"
                    size="small"
                    @click="addImg"
                    :disabled="!currentGalleryId"
                >
                  <el-icon>
                    <Plus/>
                  </el-icon>
                  添加图片
                </el-button>
                <el-button
                    type="success"
                    size="small"
                    @click="batchAddImg"
                    :disabled="!currentGalleryId"
                >
                  <el-icon>
                    <Upload/>
                  </el-icon>
                  批量上传
                </el-button>
              </div>
            </div>
          </template>

          <div v-if="!currentGalleryId" class="text-center text-gray-500 py-20">
            请先选择一个相册
          </div>

          <div v-else class="h-full flex flex-col">
            <el-tabs v-model="searchForm.imgCat" @tab-change="handleTabChange">
              <el-tab-pane label="全部" name=""></el-tab-pane>
              <el-tab-pane v-for="cat in imgCats" :key="cat" :label="cat" :name="cat"></el-tab-pane>
            </el-tabs>
            <!-- 图片网格 -->
            <el-scrollbar class="flex-1" style="max-height: calc(100vh - 250px);">
              <div
                class="image-grid p-2"
                v-infinite-scroll="loadMore"
                :infinite-scroll-disabled="loading || noMore"
                :infinite-scroll-distance="200"
              >
                <!-- 添加图片按钮 -->
                <div
                    class="image-item add-item border-2 border-dashed border-gray-300 rounded-lg flex flex-col items-center justify-center cursor-pointer hover:border-blue-400 hover:bg-blue-50 transition-colors"
                    @click="addImg"
                >
                  <el-icon class="text-3xl text-gray-400 mb-2">
                    <Plus/>
                  </el-icon>
                  <div class="text-gray-500 text-sm">添加图片</div>
                </div>

                <!-- 图片卡片 -->
                <div
                    v-for="img in galleryImgs"
                    :key="img.id"
                    class="image-item relative group border rounded-lg overflow-hidden hover:shadow-lg transition-shadow bg-gray-100"
                >
                  <div class="image-container">
                    <el-image
                        :src="imgHost + img.imgUrl"
                        fit="cover"
                        class="w-full h-full"
                        :preview-src-list="galleryImgs.map(item => imgHost + item.imgUrl)"
                        :initial-index="galleryImgs.findIndex(item => item.id === img.id)"
                        loading="lazy"
                    />

                    <!-- 悬浮操作按钮 -->
                    <div
                        class="absolute inset-0 bg-black bg-opacity-50 opacity-0 group-hover:opacity-100 transition-opacity flex items-center justify-center gap-2">
                      <el-button
                          type="primary"
                          size="small"
                          circle
                          @click.stop="editImg(img)"
                      >
                        <el-icon>
                          <Edit/>
                        </el-icon>
                      </el-button>
                      <el-button
                          type="danger"
                          size="small"
                          circle
                          @click.stop="deleteImg(img)"
                      >
                        <el-icon>
                          <Delete/>
                        </el-icon>
                      </el-button>
                    </div>

                    <!-- 图片分类标签 -->
                    <div class="absolute bottom-0 left-0 right-0 bg-black bg-opacity-70 text-white text-xs p-2 truncate flex justify-between flex-row-reverse">
                      <div>
                        {{ parseTime(img.createTime) }}
                      </div>
                      <div v-if="img.imgCat">
                        {{ img.imgCat }}
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <!-- 加载状态 -->
              <div v-if="loading" class="text-center py-4">
                <el-icon class="is-loading mr-2"><Loading /></el-icon>
                加载中...
              </div>

              <!-- 没有更多数据 -->
              <div v-else-if="noMore && galleryImgs.length > 0" class="text-center py-4 text-gray-500">
                没有更多图片了
              </div>
            </el-scrollbar>


          </div>
        </el-card>
      </div>
    </div>
    <!-- 相册编辑对话框 -->
    <el-dialog
        v-model="galleryDialogVisible"
        :title="galleryForm.id ? '编辑相册' : '添加相册'"
        width="600px"
        @close="galleryFormRef?.resetFields()"
    >
      <el-form
          ref="galleryFormRef"
          :model="galleryForm"
          label-width="100px"
          :rules="{
        galleryName: [{ required: true, message: '请输入相册名称', trigger: 'blur' }]
      }"
      >
        <el-form-item label="相册名称" prop="galleryName">
          <el-input v-model="galleryForm.galleryName" placeholder="请输入相册名称" />
        </el-form-item>

        <el-form-item label="封面图片">
          <ImageUpload
              v-model="galleryForm.galleryCover"
              :limit="1"
              :file-size="5"
          />
        </el-form-item>

        <el-form-item label="日期">
          <el-input v-model="galleryForm.galleryDate" placeholder="请输入日期" />
        </el-form-item>

        <el-form-item label="地点">
          <el-input v-model="galleryForm.galleryLocation" placeholder="请输入地点" />
        </el-form-item>

        <el-form-item label="排序">
          <el-input-number v-model="galleryForm.sort" :min="0" />
        </el-form-item>
      </el-form>

      <template #footer>
        <el-button @click="galleryDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="saveGalleryForm">保存</el-button>
      </template>
    </el-dialog>

    <!-- 图片编辑对话框 -->
    <el-dialog
        v-model="imgDialogVisible"
        :title="imgForm.id ? '编辑图片' : '添加图片'"
        width="600px"
        @close="imgFormRef?.resetFields()"
    >
      <el-form
          ref="imgFormRef"
          :model="imgForm"
          label-width="100px"
          :rules="{
        imgUrl: [{ required: true, message: '请上传图片', trigger: 'blur' }]
      }"
      >
        <el-form-item label="图片" prop="imgUrl">
          <ImageUpload
              v-model="imgForm.imgUrl"
              :limit="1"
              :file-size="10"
          />
        </el-form-item>

        <el-form-item label="图片分类">
          <el-input v-model="imgForm.imgCat" placeholder="请输入图片分类" />
        </el-form-item>
        <el-form-item label="快捷分类选择">
          <el-button v-for="cat in imgCats" :key="cat" @click="imgForm.imgCat = cat" class="mr-2 mb-2">
            {{ cat }}
          </el-button>
        </el-form-item>
      </el-form>

      <template #footer>
        <el-button @click="imgDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="saveImgForm">保存</el-button>
      </template>
    </el-dialog>

    <!-- 批量上传图片对话框 -->
    <el-dialog
        v-model="batchImgDialogVisible"
        title="批量上传图片"
        width="700px"
        @close="batchImgListStr = ''; batchImgCat = ''"
    >
      <el-form label-width="100px">
        <el-form-item label="批量上传">
          <ImageUpload
              v-model="batchImgListStr"
              :limit="20"
              :file-size="10"
          />
          <div class="text-sm text-gray-500 mt-2">
            最多可上传20张图片，单张图片不超过10MB
          </div>
        </el-form-item>

        <el-form-item label="统一分类">
          <el-input
              v-model="batchImgCat"
              placeholder="为所有图片设置统一分类（可选）"
              style="width: 300px;"
          />
        </el-form-item>
        <el-form-item label="快捷分类选择">
          <el-button v-for="cat in imgCats" :key="cat" @click="batchImgCat = cat" class="mr-2 mb-2">
            {{ cat }}
          </el-button>
        </el-form-item>

        <el-form-item>
          <div class="text-sm text-gray-600">
            <p>已选择 <span class="text-blue-600 font-medium">{{ batchAddImgList.length }}</span> 张图片</p>
            <p v-if="batchImgCat">分类：<span class="text-green-600">{{ batchImgCat }}</span></p>
          </div>
        </el-form-item>
      </el-form>

      <template #footer>
        <el-button @click="batchImgDialogVisible = false">取消</el-button>
        <el-button
            type="primary"
            @click="saveBatchImgForm"
            :disabled="batchAddImgList.length === 0"
        >
          保存 {{ batchAddImgList.length }} 张图片
        </el-button>
      </template>
    </el-dialog>
  </div>


</template>

<style scoped>
.gallery-item {
  transition: all 0.3s ease;
}

.gallery-item:hover {
  transform: translateY(-2px);
}

/* 图片网格布局 */
.image-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 16px;
  padding: 8px;
}

/* 图片项目 */
.image-item {
  aspect-ratio: 1 / 1;
  position: relative;
  min-height: 200px;
  max-height: 250px;
}

/* 图片容器 */
.image-container {
  width: 100%;
  height: 100%;
  position: relative;
}

/* 添加按钮样式 */
.add-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

/* 响应式调整 */
@media (max-width: 1200px) {
  .image-grid {
    grid-template-columns: repeat(auto-fill, minmax(180px, 1fr));
  }
  .image-item {
    min-height: 180px;
    max-height: 220px;
  }
}

@media (max-width: 768px) {
  .image-grid {
    grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
    gap: 12px;
  }
  .image-item {
    min-height: 150px;
    max-height: 180px;
  }
}

:deep(.el-card__body) {
  height: calc(100% - 60px);
  display: flex;
  flex-direction: column;
}

:deep(.el-image) {
  width: 100%;
  height: 100%;
}

:deep(.el-image__inner) {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

:deep(.el-image__error) {
  background-color: #f5f7fa;
  display: flex;
  align-items: center;
  justify-content: center;
}
</style>