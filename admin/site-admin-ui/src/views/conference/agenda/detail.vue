<script setup lang="ts">

import {useRoute} from "vue-router";
import {ref, watch, computed, type CSSProperties} from "vue";
import {getConfBasic} from "@/api/conference/basic";
import {
  delModuleRes,
  getModuleRes,
  listModuleDirRes,
  listModuleFileRes,
  saveModuleDirRes, saveModuleFileRes
} from "@/api/conference/module_res";
import {ElMessage} from "element-plus";
import {Calendar, Delete, Edit, Location, Picture, Plus} from "@element-plus/icons-vue";
import TailwindColorClassPicker from "@/components/TailwindColorClassPicker/index.vue";
import ImageUpload from "@/components/ImageUpload/index.vue";

defineOptions({
  name: "AgendaDetail",
})
const imgHost = import.meta.env.VITE_APP_BASE_API
const moduleType = '会议议程'
const route = useRoute();
const confId = ref<string>('')
const confBasic = ref<IConfBasic>()
// 目录资源列表
const moduleDirResList = ref<IConfModuleRes[]>([])
// 文件资源列表
const moduleFileResList = ref<IConfModuleRes[]>([])

// 资源表单
const moduleResForm = ref<IConfModuleRes>({})
const moduleResDialogVisible = ref(false)
// 当前选中的目录id
const currentDirResId = ref<string>('')
// 资源表单ref
const moduleResFormRef = ref()


// 获取会务信息
const getConf = async (confId: string) => {
  const {data} = await getConfBasic(confId)
  confBasic.value = data
}

// 获取会务模块内容目录资源
const getModuleDirResList = async () => {
  const {data} = await listModuleDirRes(confId.value, moduleType)
  moduleDirResList.value = data
}

// 获取会务模块内容文件资源
const getModuleFileResList = async (dirId?: string) => {
  const {data} = await listModuleFileRes(confId.value, moduleType, dirId)
  moduleFileResList.value = data
}

// 获取会务模块内容资源
const getRes = async (id: string) => {
  const {data} = await getModuleRes(id)
  moduleResForm.value = data
}

// 删除会务模块内容资源
const delRes = async (id: string) => {
  await delModuleRes(id)
}

watch(() => route.query.confId, (val) => {
  if (val) {
    confId.value = String(val ?? '')
    getConf(confId.value)
    getModuleDirResList()
  }
}, {immediate: true})

const addRes = (resType: string, dirId?: string) => {
  moduleResForm.value = {
    confId: confId.value,
    moduleType: moduleType,
    resType: resType,
    dirTitleColorClass: '',
    dirBgColorClass: '',
    dirId: dirId,
    sort: 0,
  }
  moduleResDialogVisible.value = true
}

const editRes = async (id: string) => {
  await getRes(id)
  moduleResDialogVisible.value = true
}

const selectDir = (dirId: string) => {
  currentDirResId.value = dirId
  getModuleFileResList(dirId)
}

// 保存会务模块内容资源
const saveModuleRes = async () => {
  let resType = moduleResForm.value.resType
  const api = resType ? saveModuleDirRes : saveModuleFileRes
  moduleResFormRef.value.validate().then(async () => {
    try{
      if(resType === 'D'){
        if (!moduleResForm.value.dirBgColorClass && !moduleResForm.value.dirBgUrl) {
          ElMessage.error('目录背景颜色和背景图不能同时为空')
          return
        }
      }
      if(resType === 'F'){
        // 资源后缀
        moduleResForm.value.fileType = moduleResForm.value.fileUrl?.substring(moduleResForm.value.fileUrl.lastIndexOf('.') + 1)
      }
      await api(moduleResForm.value)
      moduleResDialogVisible.value = false
      if(resType === 'D'){
        await getModuleDirResList()
      } else {
        await getModuleFileResList(currentDirResId.value)
      }
    } catch (e) {
      console.error('保存失败:', e)
    } finally {

    }
  });

}

const dialogTitle = computed(() => {
  if (moduleResForm.value?.resType === 'D') {
    return moduleResForm.value?.id ? '编辑目录' : '添加目录'
  } else {
    return moduleResForm.value?.id ? '编辑资源' : '添加资源'
  }
})

const getDirBackgroundStyle = (dirRes: IConfModuleRes): CSSProperties => {
  if (dirRes.dirBgUrl) {
    return {
      backgroundImage: `url(${imgHost}${dirRes.dirBgUrl})`,
      backgroundSize: 'cover',
      backgroundPosition: 'center',
      backgroundRepeat: 'no-repeat'
    };
  }
  return {};
}
</script>

<template>
  <div class="p-4 h-full flex flex-col gap-4 bg-[#f0f2f5]">
    <!-- 主要内容区域 -->
    <div class="flex gap-4 flex-1">
      <!-- 左侧相册列表 -->
      <div class="w-80 flex flex-col">
        <el-card shadow="never" class="h-full">
          <template #header>
            <div class="flex justify-between items-center">
              <span class="text-lg font-medium">{{confBasic?.name + '议程'}}目录列表</span>
            </div>
          </template>

          <div class="space-y-3 overflow-y-auto" style="max-height: calc(100vh - 200px);">
            <!-- 添加目录按钮 -->
            <div
                class="border-2 border-dashed border-gray-300 rounded-lg p-4 text-center cursor-pointer hover:border-blue-400 hover:bg-blue-50 transition-colors"
                @click="addRes('D')"
            >
              <el-icon class="text-2xl text-gray-400 mb-2">
                <Plus/>
              </el-icon>
              <div class="text-gray-500">添加新目录</div>
            </div>

            <!-- 目录列表 -->
            <div
                v-for="dirRes in moduleDirResList"
                :key="dirRes.id"
                class="border rounded-lg p-3 cursor-pointer transition-all hover:shadow-md flex flex-col justify-center h-25"
                :class="{
                  'border-blue-500 bg-blue-50': currentDirResId === dirRes.id,
                  'border-gray-200': currentDirResId !== dirRes.id,
                  [dirRes.dirBgColorClass!]: !dirRes.dirBgUrl
                }"
                :style="getDirBackgroundStyle(dirRes)"
                @click="selectDir(dirRes.id!)"
            >
              <div class="text-sm font-semibold text-center" :class="dirRes.dirTitleColorClass">
                {{ dirRes.dirTitle }}
              </div>
            </div>
          </div>
        </el-card>
      </div>
    </div>



    <el-dialog
        v-model="moduleResDialogVisible"
        :title="dialogTitle"
        width="600px"
        @close="moduleResFormRef?.resetFields()"
    >
      <el-form
          ref="moduleResFormRef"
          :model="moduleResForm"
          label-width="130px"
          :rules="{
            fileName: [{ required: true, message: '请输入资源名称', trigger: 'blur' }],
            fileUrl: [{ required: true, message: '请上传资源', trigger: 'change' }]
          }"
      >
        <div v-if="moduleResForm.resType === 'D'">
          <el-form-item label="目录名称" prop="dirTitle">
            <el-input v-model="moduleResForm.dirTitle" placeholder="请输入相册名称" />
          </el-form-item>

          <el-form-item label="目录名称颜色" prop="dirTitleColorClass">
            <tailwind-color-class-picker v-model="moduleResForm.dirTitleColorClass" type="text" />
          </el-form-item>

          <el-form-item label="目录背景颜色" prop="dirBgColorClass">
            <tailwind-color-class-picker v-model="moduleResForm.dirBgColorClass" type="background" />
          </el-form-item>

          <el-form-item label="目录背景图" prop="dirBgUrl">
            <ImageUpload v-model="moduleResForm.dirBgUrl" :limit="1"
                         :file-size="5"/>
          </el-form-item>

          <el-form-item label="排序">
            <el-input-number v-model="moduleResForm.sort" :min="0" />
          </el-form-item>
        </div>
        <div v-if="moduleResForm.resType === 'F'">
          <el-form-item label="资源名称" prop="fileName">
            <el-input v-model="moduleResForm.fileName" placeholder="请输入资源名称" />
          </el-form-item>
          <el-form-item label="资源" prop="fileUrl">
            <ImageUpload v-model="moduleResForm.fileUrl" :limit="1" :file-size="5"/>
          </el-form-item>
        </div>
      </el-form>
      <template #footer>
        <el-button @click="moduleResDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="saveModuleRes">保存</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<style scoped>

</style>