// @ts-ignore
import request from '@/utils/request.js'


/**
 * 获取会务模块内容目录资源列表
 * @param confId 会务id
 * @param moduleType 模块类型
 */
export function listModuleDirRes(confId: string, moduleType: string): Promise<IResult<IConfModuleRes[]>> {
  return request({
    url: '/conference/module/res/dir/list',
    method: 'get',
    params: {
      confId: confId,
      moduleType: moduleType
    }
  })
}

/**
 * 获取会务模块内容文件资源列表
 * @param confId 会务id
 * @param moduleType 模块类型
 * @param dirId 目录id
 */
export function listModuleFileRes(confId: string, moduleType: string, dirId?: string): Promise<IResult<IConfModuleRes[]>> {
  return request({
    url: '/conference/module/res/file/list',
    method: 'get',
    params: {
      confId: confId,
      moduleType: moduleType,
      dirId: dirId
    }
  })
}

/**
 * 获取会务模块内容资源
 * @param id
 */
export function getModuleRes(id: string) {
  return request({
    url: `/conference/module/res/${id}`,
    method: 'get'
  })
}

/**
 * 保存会务模块内容目录资源
 * @param data
 */
export function saveModuleDirRes(data: Partial<IConfModuleRes>) {
  return request({
    url: '/conference/module/res/dir/save',
    method: 'post',
    data: data
  })
}

/**
 * 保存会务模块内容文件资源
 * @param data
 */
export function saveModuleFileRes(data: Partial<IConfModuleRes>) {
  return request({
    url: '/conference/module/res/file/save',
    method: 'post',
    data: data
  })
}

/**
 * 删除会务模块内容资源
 * @param id
 */
export function delModuleRes(id: string) {
  return request({
    url: `/conference/module/res/${id}`,
    method: 'delete'
  })
}