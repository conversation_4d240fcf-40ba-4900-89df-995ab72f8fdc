import { defineConfig, loadEnv } from 'vite'
import path from 'path'
import createVitePlugins from './vite/plugins'

// https://vitejs.dev/config/
export default defineConfig(({ mode, command }) => {
  const env = loadEnv(mode, process.cwd())
  const { VITE_APP_ENV } = env
  return {
    // 部署生产环境和开发环境下的URL。
    // 默认情况下，vite 会假设你的应用是被部署在一个域名的根路径上
    // 例如 https://www.ruoyi.vip/。如果应用被部署在一个子路径上，你就需要用这个选项指定这个子路径。例如，如果你的应用被部署在 https://www.ruoyi.vip/admin/，则设置 baseUrl 为 /admin/。
    base: VITE_APP_ENV === 'production' ? '/hdbmhht-admin' : '/',
    build: {
      outDir: 'hdbmhht-admin'
    },
    plugins: createVitePlugins(env, command === 'build'),
    resolve: {
      // https://cn.vitejs.dev/config/#resolve-alias
      alias: {
        // 设置路径
        '~': path.resolve(__dirname, './'),
        // 设置别名
        '@': path.resolve(__dirname, './src')
      },
      // https://cn.vitejs.dev/config/#resolve-extensions
      extensions: ['.mjs', '.js', '.ts', '.jsx', '.tsx', '.json', '.vue']
    },
    // vite 相关配置
    server: {
      port: 80,
      host: true,
      open: true,
      proxy: {
        // https://cn.vitejs.dev/config/#server-proxy
        '/hdbmhht': {
          // target: 'http://*************:8083/hdbmhht',
          target: 'http://localhost:8083/hdbmhht',
          // target: 'https://trial.metinform.cn/hdbmhht',
          changeOrigin: true,
          rewrite: (p) => p.replace(/^\/hdbmhht/, '')
        },
        // 自定义地图服务代理
        '/_AMapService/v4/map/styles': {
          target: 'https://webapi.amap.com',
          changeOrigin: true,
          // rewrite: (path) => path.replace(/^\/_AMapService/, ''),
          configure: (proxy, options) => {
            proxy.on('proxyReq', (proxyReq, req, res) => {
              // 获取原始查询参数
              const url = new URL(req.url, `http://${req.headers.host}`)
              const searchParams = url.searchParams

              // 添加安全密钥
              searchParams.set('jscode', '146a4a89cd7f5ca7269c23cf61d5081d')

              // 重新构建查询字符串
              const newSearch = searchParams.toString()
              const newPath = url.pathname + (newSearch ? `?${newSearch}` : '')
              // 修改代理请求的路径
              proxyReq.path = newPath.replace(/^\/_AMapService/, '')
            })
          }
        },

        // Web服务API代理
        '/_AMapService': {
          target: 'https://restapi.amap.com',
          changeOrigin: true,
          // rewrite: (path) => path.replace(/^\/_AMapService/, ''),
          configure: (proxy, options) => {
            proxy.on('proxyReq', (proxyReq, req, res) => {
              // 获取原始查询参数
              const url = new URL(req.url, `http://${req.headers.host}`)
              const searchParams = url.searchParams

              // 添加安全密钥
              searchParams.set('jscode', '146a4a89cd7f5ca7269c23cf61d5081d')

              // 重新构建查询字符串
              const newSearch = searchParams.toString()
              const newPath = url.pathname + (newSearch ? `?${newSearch}` : '')
              // 修改代理请求的路径
              proxyReq.path = newPath.replace(/^\/_AMapService/, '')
            })
          }
        }
      }
    },
    //fix:error:stdin>:7356:1: warning: "@charset" must be the first rule in the file
    css: {
      postcss: {
        plugins: [
          {
            postcssPlugin: 'internal:charset-removal',
            AtRule: {
              charset: (atRule) => {
                if (atRule.name === 'charset') {
                  atRule.remove();
                }
              }
            }
          }
        ]
      }
    }
  }
})
